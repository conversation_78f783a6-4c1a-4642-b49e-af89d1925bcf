using Binit.Framework;
using Binit.Framework.Constants.Authentication;
using Binit.Framework.Constants.SeedEntities;
using Binit.Framework.ExceptionHandling.Types;
using Binit.Framework.Helpers;
using Binit.Framework.Interfaces.DAL;
using Binit.Framework.Interfaces.ExceptionHandling;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Data.ResponseModel;
using DevExtreme.AspNet.Mvc;
using Domain.Entities.Model;
using Domain.Entities.Model.Enum;
using Domain.Logic.BusinessLogic.DTOs;
using Domain.Logic.BusinessLogic.DTOs.SampleCageReportDTOs;
using Domain.Logic.DTOs.Export;
using Domain.Logic.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using WebApp.Models;
using JsLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Js;
using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Controllers.SampleCageReportController;

namespace WebApp.Controllers
{
    public class SampleCageReportController : Controller
    {
        private readonly ISampleCageReportService sampleCageReportService;
        private readonly IStringLocalizer<SharedResources> localizer;
        private readonly IOperationContext operationContext;
        private readonly IHenBatchService henBatchService;
        private readonly IHenWarehouseService henWarehouseService;
        private readonly IService<SampleCage> sampleCageService;
        private readonly ISampleCageReportBusinessLogic sampleCageReportBusinessLogic;
        private readonly IHenBatchBusinessLogic henBatchBusinessLogic;
        private readonly ICapacityUnitBusinessLogic capacityUnitBusinessLogic;
        private readonly ICapacityUnitService capacityUnitService;
        private readonly IExceptionManager exceptionManager;
        private readonly IHenBatchPerformanceService henBatchPerformanceService;
        private readonly RazorViewRender razorViewRender;
        private readonly IService<TenantConfiguration> tenantConfigurationService;
        private readonly IEggWeightReportBusinessLogic eggWeightReportBusinessLogic;
        private readonly int exportBatchSize = 10000; //max amount of record that can be exported

        public SampleCageReportController(
            ISampleCageReportService sampleCageReportService,
            IStringLocalizer<SharedResources> localizer,
            IOperationContext operationContext,
            IHenBatchService henBatchService,
            IHenWarehouseService henWarehouseService,
            IService<SampleCage> sampleCageService,
            ISampleCageReportBusinessLogic sampleCageReportBusinessLogic,
            ICapacityUnitBusinessLogic capacityUnitBusinessLogic,
            ICapacityUnitService capacityUnitService,
            IHenBatchBusinessLogic henBatchBusinessLogic,
            IExceptionManager exceptionManager,
            IHenBatchPerformanceService henBatchPerformanceService,
            RazorViewRender razorViewRender,
            IService<TenantConfiguration> tenantConfigurationService,
            IEggWeightReportBusinessLogic eggWeightReportBusinessLogic)
        {
            this.sampleCageReportService = sampleCageReportService;
            this.localizer = localizer;
            this.operationContext = operationContext;
            this.henBatchService = henBatchService;
            this.sampleCageService = sampleCageService;
            this.sampleCageReportBusinessLogic = sampleCageReportBusinessLogic;
            this.henBatchBusinessLogic = henBatchBusinessLogic;
            this.henWarehouseService = henWarehouseService;
            this.capacityUnitBusinessLogic = capacityUnitBusinessLogic;
            this.capacityUnitService = capacityUnitService;
            this.exceptionManager = exceptionManager;
            this.henBatchPerformanceService = henBatchPerformanceService;
            this.razorViewRender = razorViewRender;
            this.tenantConfigurationService = tenantConfigurationService;
            this.eggWeightReportBusinessLogic = eggWeightReportBusinessLogic;
        }

        #region Index
        public IActionResult Index(HenStage? henStage = null, string validationError = "")
        {
            ViewData["HenStage"] = henStage;
            ViewData["Title"] = localizer[Lang.IndexTitle];
            // This is required in order to localize datatables.
            ViewData["DatatableResources"] = JsLocalizer.GetLocalizedResources(JsLang.Datatables, localizer);
            ViewData["SampleCageReportIndexResources"] = JsLocalizer.GetLocalizedResources(JsLang.SampleCageReportIndex, this.localizer);

            InitLists(henStage);
            if (!string.IsNullOrEmpty(validationError))
                ViewData["ValidationError"] = validationError;

            IQueryable<TenantConfiguration> tenantConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId());
            ViewData["HasHenBatchCategories"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");
            ViewData["ReportsHenWarehouseAndParentBatchWeightMeasurement"] = tenantConfig.Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ReportsHenWarehouseAndParentBatchWeightMeasurement && s.Value == "True");
            ViewData["TableId"] = $"sampleCageReportTable-{henStage}";
            if (ValidateSampleCageRoles(henStage))
                return View();

            else return Forbid();
        }

        [HttpGet]
        public async Task<JsonResult> GetAll(DataSourceLoadOptions loadOptions, Dictionary<string, string> customFilters)
        {
            IQueryable<SampleCageReportDTO> sampleCageReports = sampleCageReportBusinessLogic.GetFullFiltered(customFilters);

            if (loadOptions.Take > exportBatchSize || loadOptions.Take <= 0) loadOptions.Take = exportBatchSize;

            LoadResult response = await DataSourceLoader.LoadAsync<SampleCageReportDTO>(sampleCageReports, loadOptions);
            if (response.data is List<SampleCageReportDTO>)
            {
                response.data = response.data.Cast<SampleCageReportDTO>().Select(scr =>
                {
                    return new SampleCageReportRow()
                    {
                        DT_RowId = scr.Id.ToString(),
                        Name = scr.Name,
                        Date = scr.Date,
                        FarmCode = scr.FarmCode,
                        FarmName = scr.FarmName,
                        Warehouse = scr.Warehouse,
                        Line = scr.Line,
                        HenBatch = scr.HenBatch,
                        Genetic = scr.Genetic,
                        AvgFemaleBirdWeight = scr.AvgFemaleBirdWeight.ToString(DecimalPrecision.TwoDecimal),
                        AvgMaleBirdWeight = scr.AvgMaleBirdWeight.ToString(DecimalPrecision.TwoDecimal),
                        UniformityFemale = scr.UniformityFemale.HasValue ? decimal.Round(scr.UniformityFemale.Value, 2).ToString() : "-",
                        UniformityMale = scr.UniformityMale.HasValue ? decimal.Round(scr.UniformityMale.Value, 2).ToString() : "-",
                        VariationCoefficientFemale = scr.VariationCoefficientFemale.HasValue ? decimal.Round(scr.VariationCoefficientFemale.Value, 2).ToString() : "-",
                        VariationCoefficientMale = scr.VariationCoefficientMale.HasValue ? decimal.Round(scr.VariationCoefficientMale.Value, 2).ToString() : "-",
                        HenBatchCategory = scr.HenBatchCategory,
                        HenWarehouseAvgFemaleBirdWeight = scr.HenWarehouseAvgFemaleBirdWeight.HasValue ? decimal.Round(scr.HenWarehouseAvgFemaleBirdWeight.Value, 2).ToString() : "-",
                        HenWarehouseAvgMaleBirdWeight = scr.HenWarehouseAvgMaleBirdWeight.HasValue ? decimal.Round(scr.HenWarehouseAvgMaleBirdWeight.Value, 2).ToString() : "-",
                        HenWarehouseUniformityFemale = scr.HenWarehouseUniformityFemale.HasValue ? decimal.Round(scr.HenWarehouseUniformityFemale.Value, 2).ToString() : "-",
                        HenWarehouseUniformityMale = scr.HenWarehouseUniformityMale.HasValue ? decimal.Round(scr.HenWarehouseUniformityMale.Value, 2).ToString() : "-",
                        HenWarehouseVariationCoefficientFemale = scr.HenWarehouseVariationCoefficientFemale.HasValue ? scr.HenWarehouseVariationCoefficientFemale.Value.ToString(DecimalPrecision.TwoDecimal) : "-",
                        HenWarehouseVariationCoefficientMale = scr.HenWarehouseVariationCoefficientMale.HasValue ? scr.HenWarehouseVariationCoefficientMale.Value.ToString(DecimalPrecision.TwoDecimal) : "-",

                    };
                });
            }

            return new JsonResult(response);
        }
        #endregion

        public async Task<IActionResult> Export(DataSourceLoadOptions loadOptions, Dictionary<string, string> filters,
            [FromServices] IScheduler scheduler,
            [FromServices] ISpreadSheetExportService spreadSheetExportService)
        {
            filters.Add("entity", nameof(SampleCageReportExport));
            filters.Add("host", Request.Host.Value);
            filters.Add("scheme", Request.Scheme);

            int totalCount = int.Parse(filters["totalCount"]);

            if (totalCount < 2000)
            {
                var (uri, file) = await spreadSheetExportService.Export(loadOptions, filters);
                return Created(uri, new { file });
            }
            else
            {
                string loadOptionsJson = JsonConvert.SerializeObject(loadOptions);
                string filtersJson = JsonConvert.SerializeObject(filters);
                string email = operationContext.GetUsername();

                JobDataMap data = new JobDataMap
                {
                    ["loadOptions"] = loadOptionsJson,
                    ["filters"] = filtersJson,
                    ["email"] = email,
                    ["culture"] = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName

                };
                await scheduler.TriggerJob(new JobKey("ExportJob", "Startup"), data);

                return Accepted(new
                {
                    message = localizer.GetString(
                    Binit.Framework.Localization.LocalizationConstants.DomainLogic.JobScheduler.Jobs.ExportJob.ExportResultMessage,
                    email).Value
                });
            }
        }

        #region Create
        public IActionResult Create(HenStage? henStage = null)
        {
            if (ValidateSampleCageRoles(henStage))
            {
                ViewData["Title"] = localizer[Lang.CreateTitle];
                ViewData["HenStage"] = henStage;

                if (henBatchService.GetAllFull(henStage).All(hb => hb.DateEnd == null && (hb.HenAmountFemale + hb.HenAmountMale == 0)))
                    return RedirectToAction("Index", "SampleCageReport", new { henStage, validationError = localizer[Lang.AllHenBatchesAreEmpty] });

                ViewData["HenBatches"] = GetHenBatches(henStage);
                ViewData["Action"] = "Create";

                ViewData["HasHenBatchCategories"] = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

                return View("CreateOrEdit", new SampleCageReportViewModel(henStage.Value));
            }

            else return Forbid();
        }

        [HttpPost]
        public async Task<IActionResult> Create(SampleCageReportViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                viewModel.SampleCageMeasurements = viewModel.SampleCageMeasurements.Where(scm => !scm.IsExcluded).ToList();
                viewModel.SampleCageMeasurements = SetRelativeValues(viewModel.SampleCageMeasurements);
                SampleCageReport sampleCageReport = viewModel.ToEntity();
                try
                {
                    await sampleCageReportBusinessLogic.CreateAndAddToHenBatchPerformanceAsync(sampleCageReport);

                    return RedirectToAction("Index", new { henStage = viewModel.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }
            ViewData["Title"] = localizer[Lang.CreateTitle];
            ViewData["HenStage"] = viewModel.HenStage;
            ViewData["HenBatches"] = GetHenBatches(viewModel.HenStage);
            ViewData["Action"] = "Create";
            ViewData["HasHenBatchCategories"] = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            List<SelectListItem> units = ListWithGrams;
            foreach (SampleCageMeasurementViewModel cage in viewModel.SampleCageMeasurements)
            {
                cage.FemaleBirdWeightMeasures = units;
                cage.MaleBirdWeightMeasures = units;
            }
            return View("CreateOrEdit", viewModel);
        }
        #endregion

        #region Create from warehouse
        public IActionResult CreateFromWarehouse(HenStage? henStage = null, bool reportConfig = false)
        {
            if (!ValidateSampleCageRoles(henStage))
                return Forbid();

            ViewData["Title"] = localizer[Lang.CreateTitle];
            ViewData["HenStage"] = henStage;

            if (henBatchService.GetAllFull(henStage).All(hb => hb.DateEnd == null && (hb.HenAmountFemale + hb.HenAmountMale == 0)))
                return RedirectToAction("Index", "SampleCageReport", new { henStage, validationError = localizer[Lang.AllHenBatchesAreEmpty] });

            ViewData["Warehouses"] = GetWarehouses(henStage);

            return View("CreateFromWarehouse", new WarehouseSampleCageReportViewModel(henStage.Value) { ReportConfig = reportConfig });
        }

        [HttpPost]
        public async Task<IActionResult> CreateFromWarehouse(WarehouseSampleCageReportViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                viewModel.SampleCageMeasurements = viewModel.SampleCageMeasurements.Where(scm => !scm.IsExcluded).ToList();
                viewModel.SampleCageMeasurements = SetRelativeValues(viewModel.SampleCageMeasurements);
                List<SampleCageReport> sampleCageReports = viewModel.ToEntity();
                try
                {
                    await sampleCageReportBusinessLogic.CreateReports(sampleCageReports);

                    return RedirectToAction("Index", new { henStage = viewModel.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }
            ViewData["Title"] = localizer[Lang.CreateTitle];
            ViewData["HenStage"] = viewModel.HenStage;
            ViewData["Warehouses"] = GetWarehouses(viewModel.HenStage, viewModel.WarehouseId);

            List<SelectListItem> units = ListWithGrams;
            foreach (SampleCageMeasurementViewModel cage in viewModel.SampleCageMeasurements)
            {
                cage.FemaleBirdWeightMeasures = units;
                cage.MaleBirdWeightMeasures = units;
            }

            return View("CreateFromWarehouse", viewModel);
        }
        #endregion

        #region Edit
        [HttpGet]
        public IActionResult Edit(Guid id)
        {
            // Get sample cages from database
            SampleCageReport sampleCageReport = sampleCageReportService.GetFull(id);

            if (!ValidateSampleCageRoles(sampleCageReport.HenBatchPerformance.HenBatch.HenStage))
                return Forbid();

            ViewData["HenStage"] = sampleCageReport.HenBatchPerformance.HenBatch.HenStage;
            ViewData["HenBatches"] = new List<SelectListItem>();
            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.EditTitle];
            ViewData["Action"] = "Edit";
            bool hasHenBatchCategories = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");
            ViewData["HasHenBatchCategories"] = hasHenBatchCategories;
            SampleCageReportViewModel model = new SampleCageReportViewModel(sampleCageReport, hasHenBatchCategories);


            List<SelectListItem> units = ListWithGrams;
            foreach (SampleCageMeasurementViewModel measurement in model.SampleCageMeasurements)
            {
                measurement.FemaleBirdWeightMeasures = units;
                measurement.MaleBirdWeightMeasures = units;
                measurement.AvgFemaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, measurement.AvgFemaleBirdWeight)));
                measurement.AvgMaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, measurement.AvgMaleBirdWeight)));

            }
            // Return view with material info
            return View("CreateOrEdit", model);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(SampleCageReportViewModel sampleCageReport)
        {
            // Check if model is valid
            if (ModelState.IsValid)
            {
                sampleCageReport.SampleCageMeasurements = SetRelativeValues(sampleCageReport.SampleCageMeasurements);
                try
                {
                    await sampleCageReportBusinessLogic.UpdateAndAddToHenBatchPerformanceAsync(sampleCageReport.ToEntity());
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }

                return RedirectToAction("Index", new { henStage = sampleCageReport.HenStage });
            }
            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.EditTitle];
            ViewData["HenStage"] = sampleCageReport.HenStage;
            ViewData["HenBatches"] = new List<SelectListItem>();
            ViewData["Action"] = "Edit";
            ViewData["HasHenBatchCategories"] = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");

            List<SelectListItem> units = ListWithGrams;
            foreach (SampleCageMeasurementViewModel cage in sampleCageReport.SampleCageMeasurements)
            {
                cage.FemaleBirdWeightMeasures = units;
                cage.MaleBirdWeightMeasures = units;
            }

            return View("CreateOrEdit", sampleCageReport);
        }
        #endregion

        #region Edit From Warehouse
        public IActionResult EditFromWarehouse(Guid id)
        {
            // Get sample cages from database
            SampleCageReport sampleCageReport = sampleCageReportService.GetFull(id);

            if (!ValidateSampleCageRoles(sampleCageReport.HenBatchPerformance.HenBatch.HenStage))
                return Forbid();
            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.EditFromWarehouse];
            ViewData["HenStage"] = sampleCageReport.HenBatchPerformance.HenBatch.HenStage;
            bool hasHenBatchCategories = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");
            bool reportConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ReportsHenWarehouseAndParentBatchWeightMeasurement && s.Value == "True");

            WarehouseSampleCageReportViewModel model = new WarehouseSampleCageReportViewModel(sampleCageReport, reportConfig)
            {
                WarehouseId = sampleCageReport.HenBatch.Line.WarehouseId.ToString(),
                WarehouseName = sampleCageReport.HenBatch.Line.Warehouse.Name,
                HasFemale = (sampleCageReport.HenBatch.HenAmountFemale > 0),
                HasMale = (sampleCageReport.HenBatch.HenAmountMale > 0)
            };
            if (sampleCageReport.HenWarehouseAvgFemaleBirdWeight.HasValue)
                model.HenWarehouseAvgFemaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, model.HenWarehouseAvgFemaleBirdWeight.HasValue ? model.HenWarehouseAvgFemaleBirdWeight.Value : 0)));

            if (sampleCageReport.HenWarehouseAvgMaleBirdWeight.HasValue)
                model.HenWarehouseAvgMaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, model.HenWarehouseAvgMaleBirdWeight.HasValue ? model.HenWarehouseAvgMaleBirdWeight.Value : 0)));

            List<SelectListItem> units = ListWithGrams;
            model.HenWarehouseFemaleBirdWeightMeasures = units;
            model.HenWarehouseMaleBirdWeightMeasures = units;

            // Return view with material info
            return View("EditFromWarehouse", model);
        }

        [HttpPost]
        public async Task<IActionResult> EditFromWarehouse(WarehouseSampleCageReportViewModel viewModel)
        {
            // Check if model is valid
            if (ModelState.IsValid)
            {
                viewModel.HenWarehouseAvgFemaleBirdWeight = !string.IsNullOrEmpty(viewModel.HenWarehouseFemaleBirdWeightMeasureId)
                    ? capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(viewModel.HenWarehouseFemaleBirdWeightMeasureId), viewModel.HenWarehouseAvgFemaleBirdWeight.Value)
                    : viewModel.HenWarehouseAvgFemaleBirdWeight;
                viewModel.HenWarehouseAvgMaleBirdWeight = !string.IsNullOrEmpty(viewModel.HenWarehouseMaleBirdWeightMeasureId)
                   ? capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(viewModel.HenWarehouseMaleBirdWeightMeasureId), viewModel.HenWarehouseAvgMaleBirdWeight.Value)
                   : viewModel.HenWarehouseAvgMaleBirdWeight;
                try
                {
                    await sampleCageReportBusinessLogic.UpdateHenWarehouseReports(viewModel.ToEntityFromWarehouse());

                    return RedirectToAction("Index", new { henStage = viewModel.HenStage });
                }
                catch (ValidationException ex)
                {
                    foreach (KeyValuePair<string, string> error in ex.Errors)
                        ModelState.AddModelError(string.Empty, error.Value);
                }
            }
            // Setup View Title and Mode
            ViewData["Title"] = localizer[Lang.EditFromWarehouse];
            ViewData["HenStage"] = viewModel.HenStage;

            List<SelectListItem> units = ListWithGrams;
            viewModel.HenWarehouseFemaleBirdWeightMeasures = units;
            viewModel.HenWarehouseMaleBirdWeightMeasures = units;

            bool hasHenBatchCategories = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");
            bool reportConfig = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ReportsHenWarehouseAndParentBatchWeightMeasurement && s.Value == "True");

            return View("EditFromWarehouse", viewModel);
        }

        #endregion

        #region Details
        public IActionResult Details(Guid id)
        {
            ViewData["Title"] = localizer[Lang.DetailsTitle];

            bool hasHenBatchCategories = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.HasHenBatchCategories && s.Value == "True");
            ViewData["HasReportsHenWarehouseAndParentBatchWeightMeasurement"] = tenantConfigurationService.GetAll().Where(t => t.TenantId == operationContext.GetUserTenantId()).Any(s => s.TenantConfigurationEnum == TenantConfigurationEnum.ReportsHenWarehouseAndParentBatchWeightMeasurement && s.Value == "True");

            // Get SampleCageReport from database
            SampleCageReport sampleCageReport = sampleCageReportService.GetFull(id);
            SampleCageReportViewModel model = new SampleCageReportViewModel(sampleCageReport, hasHenBatchCategories);
            model.HenWarehouseAvgFemaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, model.HenWarehouseAvgFemaleBirdWeight.HasValue ? model.HenWarehouseAvgFemaleBirdWeight.Value : 0)));
            model.HenWarehouseAvgMaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, model.HenWarehouseAvgMaleBirdWeight.HasValue ? model.HenWarehouseAvgMaleBirdWeight.Value : 0)));

            string symbol = capacityUnitService.Get(CapacityUnits.Grams).Symbol;
            model.HenWarehouseFemaleBirdWeightMeasureId = symbol;
            model.HenWarehouseMaleBirdWeightMeasureId = symbol;
            foreach (SampleCageMeasurementViewModel measurement in model.SampleCageMeasurements)
            {
                measurement.AvgFemaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, measurement.AvgFemaleBirdWeight)));
                measurement.AvgMaleBirdWeight = Convert.ToDecimal(String.Format("{0:0.00}", capacityUnitBusinessLogic.ConvertValue(CapacityUnits.Grams, measurement.AvgMaleBirdWeight)));
                measurement.FemaleBirdWeightMeasure = symbol;
                measurement.MaleBirdWeightMeasure = symbol;
            }
            ViewData["IsHenBatchChild"] = sampleCageReport.HenBatch.ParentId.HasValue;
            // Return view with SampleCageReport info
            return View(model);
        }
        #endregion

        #region Delete
        public async Task<JsonResult> Delete(Guid id)
        {
            string message;
            try
            {
                SampleCageReport sampleCageReport = sampleCageReportService.GetFull(id);
                if (!ValidateSampleCageRolesToDelete(sampleCageReport.HenBatchPerformance.HenBatch.HenStage))
                    return new JsonResult(new { localizer[Lang.HandleUnauthorizedEx].Value });
                await sampleCageReportBusinessLogic.DeleteAndRemoveFromHenBatchPerformanceAsync(id);
                HttpContext.Response.StatusCode = (int)HttpStatusCode.OK;
                message = localizer[Lang.DeleteSuccess];
            }
            catch (NotFoundException ex)
            {
                HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                message = ex.Message;
            }
            catch (UserException ex)
            {
                HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = ex.Message;
            }
            catch (Exception)
            {
                HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                message = localizer[Lang.DeleteUnexpectedError];
            }

            return new JsonResult(new
            {
                message
            });
        }
        #endregion

        #region Index Lists
        /// <summary>
        /// Gets farms, clusters, warehouses and genetics for sample cage report's index view filters.
        /// </summary>
        private void InitLists(HenStage? henStage)
        {
            IQueryable<HenBatch> batches = henBatchService.GetAll(asNoTracking: true)
                .Include(hb => hb.Line).ThenInclude(l => l.Warehouse).ThenInclude(w => w.Cluster);

            if (henStage.HasValue)
                batches = batches.Where(b => b.HenStage == henStage);

            // Farms
            ViewData["Farms"] = batches.Select(b => b.Farm)
                                       .Distinct()
                                       .OrderBy(f => f.Name)
                                       .Select(f => new SelectListItem(f.Name, f.Id.ToString()))
                                       .ToList();

            // Clusters
            ViewData["Clusters"] = batches.Select(b => b.Line.Warehouse.Cluster)
                                          .Distinct()
                                          .OrderBy(c => c.Name)
                                          .Select(c => new SelectListItem(c.Name, c.Id.ToString()))
                                          .ToList();

            // Warehouses
            ViewData["Warehouses"] = batches.Select(b => b.Line.Warehouse)
                                            .Distinct()
                                            .OrderBy(w => w.Name)
                                            .Select(w => new SelectListItem(w.Name, w.Id.ToString()))
                                            .ToList();

            // Lines
            ViewData["Lines"] = batches.Select(b => b.Line)
                                       .Distinct()
                                       .OrderBy(l => l.Name)
                                       .Select(l => new SelectListItem(l.Name, l.Id.ToString()))
                                       .ToList();

            // Parent henbatches
            ViewData["HenBatches"] = batches.Where(hb => hb.ParentId == null)
                                            .OrderBy(hb => hb.Code)
                                            .Select(hb => new SelectListItem(hb.Code, hb.Id.ToString()))
                                            .ToList();

            // Genetics
            ViewData["Genetics"] = batches.Select(b => b.Genetic)
                                          .Distinct()
                                          .OrderBy(g => g.Name)
                                          .Select(g => new SelectListItem(g.Name, g.Id.ToString()))
                                          .ToList();
        }

        #endregion

        #region filters refresh
        /// <summary>
        /// Gets clusters from a farm to use in sample cage report cluster filter.
        /// </summary>
        public List<SelectListItem> GetClustersByFarm(Guid? farmId, HenStage? henStage)
        {
            if (!ValidateSampleCageRoles(henStage))
                return new List<SelectListItem>();
            return henBatchBusinessLogic.GetClusters(henStage, farmId);
        }

        /// <summary>
        /// Gets hen warehouses to use in sample cage report index cluster filter.
        /// </summary>
        public List<SelectListItem> GetWarehousesByCluster(Guid? clusterId, HenStage? henStage)
        {
            if (!ValidateSampleCageRoles(henStage))
                return new List<SelectListItem>();
            return henBatchBusinessLogic.GetWarehouses(henStage, clusterId);
        }

        /// <summary>
        /// Gets lines from a hen warehouse to use in sample cage report index cluster filter.
        /// </summary>
        public List<SelectListItem> GetLinesByWarehouse(Guid? warehouseId, HenStage? henStage)
        {
            if (!ValidateSampleCageRoles(henStage))
                return new List<SelectListItem>();
            return henBatchBusinessLogic.GetLines(henStage, warehouseId);
        }

        /// <summary>
        /// Gets all henbatches to use in sample cage report index cluster filter.
        /// </summary>
        public List<SelectListItem> GetHenBatchesByLine(Guid? lineId, HenStage? henStage)
        {
            if (!ValidateSampleCageRoles(henStage))
                return new List<SelectListItem>();
            return henBatchBusinessLogic.GetHenBatches(henStage, lineId);
        }

        public List<SelectListItem> GetHenBatchesParents(HenStage? henStage)
        {
            return henBatchService.GetAllFull(henStage).Where(hb => hb.DateEnd == null && hb.ParentId == null)
                .OrderBy(hb => hb.Code)
                .Select(c => new SelectListItem(c.Code, c.Id.ToString())).ToList();
        }

        public List<SelectListItem> GetDistributionForHenBatch(string selectedHenBatch)
        {
            return henBatchService.GetAllFull().Where(hb => hb.ParentId.ToString() == selectedHenBatch)
                .Select(hb => new SelectListItem($"{hb.Code} | {hb.Line.Warehouse} | {hb.Line}", hb.Id.ToString()))
                .ToList();
        }
        #endregion

        #region Create Methods
        public async Task<IActionResult> GetHens(Guid henBatchId)
        {
            IQueryable<HenBatch> henBatches = henBatchService.GetAll().Include(hb => hb.Category)
                .Where(hb => hb.Id == henBatchId || hb.ParentId == henBatchId);
            var hens = new
            {
                HasFemaleHen = henBatches.Any(hb => hb.HenAmountFemale > 0),
                HasMaleHen = henBatches.Any(hb => hb.HenAmountMale > 0),
                Category = henBatches.FirstOrDefault(hb => hb.Id == henBatchId).CategoryId.HasValue ? henBatches.FirstOrDefault(hb => hb.Id == henBatchId).Category.Name : " "
            };
            return Ok(hens);
        }

        public IActionResult CreateBreedingWeightReport()
        {
            ViewData["Title"] = "Lançamento de Peso";
            InitLists(HenStage.Breeding);
            return View("CreateBreedingWeightReport");
        }

        public IActionResult CreateLayingWeightReport()
        {
            ViewData["Title"] = "Lançamento de Peso";
            InitLists(HenStage.Laying);
            return View("CreateLayingWeightReport");
        }

        [HttpPost]
        public async Task<IActionResult> CreateSampleCageFromTable(
                [FromBody] CreateSampleCageReportFromTableDTO dto)
        {
            try
            {
                var entities = await sampleCageReportBusinessLogic.CreateReportsFromTableAsync(dto);

                // Save processed warehouses
                if (dto.ProcessedWarehouses != null && dto.ProcessedWarehouses.Count > 0)
                {
                    DateTime reportDate = DateTime.Parse(dto.ReportDate);
                    Guid henBatchId = dto.Reports.First().HenBatchId;

                    // Save processed warehouse IDs
                    await sampleCageReportBusinessLogic.SaveProcessedWarehouseIds(henBatchId, reportDate, dto.ProcessedWarehouses);
                }

                // Process egg weight reports for laying hens
                foreach (var report in dto.Reports)
                {
                    if (report.EggWeight.HasValue && report.EggWeight.Value > 0)
                    {
                        try
                        {
                            await eggWeightReportBusinessLogic.CreateEggWeightReportFromSampleCageReport(
                                report.HenBatchId,
                                DateTime.Parse(dto.ReportDate),
                                report.EggWeight.Value,
                                report.SampleCageId);
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                }

                var result = entities.Select(e =>
                {
                    var measurements = e.SampleCageMeasurement;

                    var allWeights = measurements
                        .SelectMany(m => new[]
                        {
                m.AvgFemaleBirdWeight > 0 ? m.AvgFemaleBirdWeight : (decimal?)null,
                m.AvgMaleBirdWeight   > 0 ? m.AvgMaleBirdWeight   : (decimal?)null
                        })
                        .Where(x => x.HasValue)
                        .Select(x => x.Value)
                        .ToList();

                    decimal averageBatch = allWeights.Any()
                        ? allWeights.Average()
                        : 0m;

                    return new SampleCageReportResultDTO
                    {
                        Id = e.Id,
                        AverageWeight = averageBatch
                    };
                });

                return Ok(new { reports = result });
            }
            catch (ValidationException ex)
            {
                return BadRequest(new
                {
                    message = ex.Message,
                    errors = ex.Errors
                });
            }
        }

        // Get Samplecages
        [HttpGet]
        public IActionResult RevalidateDate(Guid henBatchId, string date)
        {
            DateTime reportDate = DateTime.Parse(date);

            // Allow backdated entries - check if there's a performance record for the week instead of exact date
            int week = henBatchService.GetCurrentWeekNumberForDate(henBatchId, reportDate);
            var performance = henBatchPerformanceService.GetAll()
                .FirstOrDefault(hbp => hbp.HenBatchId == henBatchId && hbp.WeekNumber == week);

            if (performance == null)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.DoesntHaveHenReports])).Message
                });
            }
            else
            {
                return Ok();
            }
        }
        public IActionResult GetSampleCages(int inputsLength, Guid henBatchId, string date)
        {
            DateTime reportDate = DateTime.Parse(date);

            // Allow backdated entries - check if there's a performance record for the week instead of exact date
            int week = henBatchService.GetCurrentWeekNumberForDate(henBatchId, reportDate);
            var performance = henBatchPerformanceService.GetAll()
                .FirstOrDefault(hbp => hbp.HenBatchId == henBatchId && hbp.WeekNumber == week);

            if (performance == null)
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.DoesntHaveHenReports])).Message
                });
            }

            // get sample cages
            IQueryable<SampleCage> cages = sampleCageService.GetAll().Where(sc => sc.HenBatchId == henBatchId && sc.Active);

            if (cages.Count() > 0)
            {
                List<SelectListItem> units = ListWithGrams;
                HenBatch henBatch = henBatchService.Get(henBatchId);
                SampleCageReportViewModel sampleCages = new SampleCageReportViewModel(henBatch);
                foreach (SampleCage item in cages)
                {
                    for (int i = 0; i < inputsLength; i++)
                    {
                        sampleCages.SampleCageMeasurements.Add(new SampleCageMeasurementViewModel(item)
                        {
                            FemaleBirdWeightMeasures = units,
                            MaleBirdWeightMeasures = units,
                            IsExcluded = inputsLength > 1
                        });
                    }
                }
                ;

                return ViewComponent("SampleCages", new { index = inputsLength - 1, model = sampleCages, henStage = henBatchService.Get(henBatchId).HenStage });
            }
            else // no sample cages
            {
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.NoSampleCages])).Message
                });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetSampleCagesFromWarehouse(Guid warehouseId, string date, bool tenantConfig)
        {
            List<HenBatch> henBatches = henBatchService.GetAllFromWarehouse(warehouseId).ToList();

            if (!henBatches.Any())
            { // if the warehouse is empty return error
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.NoHenBateches])).Message
                });
            }

            (string error, List<Guid> henBatches) result = ValidateHenBatches(henBatches, DateTime.Parse(date));
            if (!result.henBatches.Any())
            { // there are not available hen batches
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.NoHenBatechesAvailable] + "</br>" + result.error)).Message
                });
            }

            IQueryable<SampleCage> cages = sampleCageService.GetAll()
                .Where(sc => result.henBatches.Any(hb => hb == sc.HenBatchId) && sc.Active);

            if (cages.Count() > 0)
            {
                List<SelectListItem> units = ListWithGrams;
                WarehouseSampleCageReportViewModel sampleCages = new WarehouseSampleCageReportViewModel(cages.FirstOrDefault().HenBatch.HenStage);
                foreach (SampleCage item in cages)
                {
                    sampleCages.SampleCageMeasurements.Add(new SampleCageMeasurementViewModel(item)
                    {
                        HasFemaleHen = item.HenBatch.HenAmountFemale > 0,
                        HasMaleHen = item.HenBatch.HenAmountMale > 0,
                        FemaleBirdWeightMeasures = units,
                        MaleBirdWeightMeasures = units,
                        CageName = item.HenBatch.ToString() + " - " + item.Name,
                        IsExcluded = true
                    });
                }
                ;

                sampleCages.SampleCageMeasurements = sampleCages.SampleCageMeasurements.OrderBy(scm => scm.CageName).ToList();


                if (tenantConfig)
                    sampleCages.SampleCageMeasurements.Insert(0, new SampleCageMeasurementViewModel()
                    {
                        HasFemaleHen = cages.Any(c => c.HenBatch.HenAmountFemale > 0),
                        HasMaleHen = cages.Any(c => c.HenBatch.HenAmountMale > 0),
                        CageName = localizer[Lang.WarehouseSampleCage],
                        FemaleBirdWeightMeasures = units,
                        MaleBirdWeightMeasures = units,
                        WarehouseMeasures = true
                    });

                return new JsonResult(new
                {
                    error = result.error,
                    sampleCages = await razorViewRender.RenderToStringAsync("Shared/Components/WarehouseSampleCages/Default", sampleCages, new Dictionary<string, object>() { { "model", sampleCages }, { "henStage", cages.FirstOrDefault().HenBatch.HenStage }, { "editable", true } })
                });

            }
            else
            { // no sample cages
                this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                return new JsonResult(new
                {
                    exceptionManager.Handle(new UserException(this.localizer[Lang.NoSampleCages])).Message
                });
            }
        }

        [HttpGet]
        public JsonResult GetWarehousesWithLinesByBatch(Guid henBatchId, string date = null)
        {
            // If date is provided, validate that hen reports exist for the selected date
            if (!string.IsNullOrEmpty(date))
            {
                DateTime reportDate = DateTime.Parse(date);
                int week = henBatchService.GetCurrentWeekNumberForDate(henBatchId, reportDate);
                var performance = henBatchPerformanceService.GetAll()
                    .FirstOrDefault(hbp => hbp.HenBatchId == henBatchId && hbp.WeekNumber == week);

                if (performance == null)
                {
                    this.HttpContext.Response.StatusCode = (int)HttpStatusCode.NotFound;
                    return Json(new
                    {
                        message = exceptionManager.Handle(new UserException(this.localizer[Lang.DoesntHaveHenReports])).Message
                    });
                }
            }

            var batchIds = henBatchService
                 .GetAll()
                 .Where(hb => hb.Id == henBatchId || hb.ParentId == henBatchId)
                 .Select(hb => hb.Id)
                 .ToList();

            var sampleCages = sampleCageService.GetAll()
                .Where(sc => batchIds.Contains(sc.HenBatchId) && sc.Active)
                .Select(sc => new
                {
                    sc.Id,
                    sc.HenBatchId,
                    sc.HenBatch.LineId,
                    sc.HenBatch.HenAmountFemale,
                    sc.HenBatch.HenAmountMale
                })
                .Where(sc => sc.HenAmountFemale > 0 || sc.HenAmountMale > 0)
                .ToList();

            var warehouses = henWarehouseService.GetAllByHenBatch(henBatchId)
               .Include(hw => hw.Lines)
               .ToList();

            var data = warehouses.Select(hw => new
            {
                warehouseId = hw.Id,
                name = hw.Name,
                lines = hw.Lines
                .Where(l => sampleCages.Any(sc => sc.LineId == l.Id))
                .Select(l => new
                {
                    id = l.Id,
                    name = l.Name,
                    sampleCageId = sampleCages.First(sc => sc.LineId == l.Id).Id,
                    henBatchId = sampleCages.First(sc => sc.LineId == l.Id).HenBatchId,
                    henBatch = new
                    {
                        henBatchId,
                        code = henBatchService.Get(henBatchId).Code,
                        henAmountFemale = henBatchService.Get(henBatchId).HenAmountFemale,
                        henAmountMale = henBatchService.Get(henBatchId).HenAmountMale
                    }
                })
                .ToList()
            })
            .Where(hw => hw.lines.Any())
            .ToList();

            return Json(new { warehouses = data });
        }

        [HttpGet]
        public IActionResult GetSampleCagesByBatch(Guid henBatchId, DateTime date)
        {
            // Validate that hen reports exist for the selected date
            int week = henBatchService.GetCurrentWeekNumberForDate(henBatchId, date);
            var performance = henBatchPerformanceService.GetAll()
                .FirstOrDefault(hbp => hbp.HenBatchId == henBatchId && hbp.WeekNumber == week);

            if (performance == null)
            {
                return NotFound(new { message = localizer[Lang.DoesntHaveHenReports] });
            }

            var cages = sampleCageService
              .GetAll()
              .Where(sc => sc.HenBatchId == henBatchId && sc.Active)
              .Select(sc => new
              {
                  sc.Id,
                  sc.Name,
                  sc.HenBatch.HenAmountFemale,
                  sc.HenBatch.HenAmountMale
              })
              .ToList();
            return Ok(cages);
        }

        [HttpGet]
        public IActionResult GetExistingSampleCageReports(Guid henBatchId, string date)
        {
            try
            {
                DateTime reportDate = DateTime.Parse(date);

                // Get the week number for the date
                int week = henBatchService.GetCurrentWeekNumberForDate(henBatchId, reportDate);

                // Get the performance for this week
                var performance = henBatchPerformanceService.GetAll()
                    .FirstOrDefault(hbp => hbp.HenBatchId == henBatchId && hbp.WeekNumber == week);

                if (performance == null)
                {
                    return NotFound(new { message = "No performance data found for this date and batch" });
                }

                // Get all batch IDs (parent and children)
                var batchIds = henBatchService
                    .GetAll()
                    .Where(hb => hb.Id == henBatchId || hb.ParentId == henBatchId)
                    .Select(hb => hb.Id)
                    .ToList();

                // Get sample cage reports for this date and batch
                var reports = sampleCageReportService.GetAll()
                    .Where(scr => batchIds.Contains(scr.HenBatchId) && scr.Date.Date == reportDate.Date)
                    .Include(scr => scr.HenBatch).ThenInclude(hb => hb.Line).ThenInclude(l => l.Warehouse)
                    .Include(scr => scr.SampleCageMeasurement)
                    .Include(scr => scr.CagesRelation)
                    .ToList();

                if (!reports.Any())
                {
                    return NotFound(new { message = "No reports found for this date and batch" });
                }

                // Group reports by warehouse - with null checks
                var warehouseReports = reports
                    .Where(r => r.HenBatch?.Line?.WarehouseId != null)
                    .GroupBy(r => r.HenBatch.Line.WarehouseId)
                    .Select(g => new
                    {
                        warehouseId = g.Key,
                        warehouseName = g.FirstOrDefault()?.HenBatch?.Line?.Warehouse?.Name ?? "N/D",
                        averages = new
                        {
                            weightF = g.Where(r => r.HenWarehouseAvgFemaleBirdWeight > 0)
                                      .Select(r => r.HenWarehouseAvgFemaleBirdWeight * 1000)
                                      .DefaultIfEmpty(0)
                                      .FirstOrDefault(),
                            cvF = g.Where(r => r.HenWarehouseVariationCoefficientFemale > 0)
                                  .Select(r => r.HenWarehouseVariationCoefficientFemale)
                                  .DefaultIfEmpty(0)
                                  .FirstOrDefault(),
                            unifF = g.Where(r => r.HenWarehouseUniformityFemale > 0)
                                    .Select(r => r.HenWarehouseUniformityFemale)
                                    .DefaultIfEmpty(0)
                                    .FirstOrDefault(),
                            eggWeight = g.Where(r => r.AvgEggWeight > 0)
                                        .Select(r => r.AvgEggWeight * 1000)
                                        .DefaultIfEmpty(0)
                                        .FirstOrDefault(),
                            weightM = g.Where(r => r.HenWarehouseAvgMaleBirdWeight > 0)
                                      .Select(r => r.HenWarehouseAvgMaleBirdWeight * 1000)
                                      .DefaultIfEmpty(0)
                                      .FirstOrDefault(),
                            cvM = g.Where(r => r.HenWarehouseVariationCoefficientMale > 0)
                                  .Select(r => r.HenWarehouseVariationCoefficientMale)
                                  .DefaultIfEmpty(0)
                                  .FirstOrDefault(),
                            unifM = g.Where(r => r.HenWarehouseUniformityMale > 0)
                                    .Select(r => r.HenWarehouseUniformityMale)
                                    .DefaultIfEmpty(0)
                                    .FirstOrDefault()
                        },
                        boxes = g.Select(r => new
                        {
                            reportId = r.Id,
                            sampleCageId = r.CagesRelation?.FirstOrDefault()?.SampleCageId,
                            henBatchId = r.HenBatchId,
                            warehouseId = r.HenBatch?.Line?.WarehouseId,
                            aviary = r.HenBatch?.Line?.Warehouse?.Name ?? "N/D",
                            box = r.HenBatch?.Line?.Name ?? "N/D",
                            weightF = r.AvgFemaleBirdWeight > 0 ? r.AvgFemaleBirdWeight * 1000 : 0,
                            cvF = r.AvgVariationCoefficientFemale,
                            unifF = r.AvgUniformityFemale,
                            eggWeight = r.AvgEggWeight > 0 ? r.AvgEggWeight * 1000 : 0,
                            weightM = r.AvgMaleBirdWeight > 0 ? r.AvgMaleBirdWeight * 1000 : 0,
                            cvM = r.AvgVariationCoefficientMale,
                            unifM = r.AvgUniformityMale,
                            isModified = false
                        }).ToList()
                    })
                    .ToList();

                // Calculate batch averages with null checks and empty collection handling
                var batchAverages = new
                {
                    weightF = reports.Where(r => r.AvgFemaleBirdWeight > 0)
                             .Select(r => r.AvgFemaleBirdWeight)
                             .DefaultIfEmpty(0) // Handle empty collection
                             .Average() * 1000,
                    cvF = reports.Where(r => r.AvgVariationCoefficientFemale > 0)
                          .Select(r => r.AvgVariationCoefficientFemale)
                          .DefaultIfEmpty(0)
                          .Average(),
                    unifF = reports.Where(r => r.AvgUniformityFemale > 0)
                            .Select(r => r.AvgUniformityFemale)
                            .DefaultIfEmpty(0)
                            .Average(),
                    eggWeight = reports.Where(r => r.AvgEggWeight > 0)
                                .Select(r => r.AvgEggWeight)
                                .DefaultIfEmpty(0)
                                .Average() * 1000,
                    weightM = reports.Where(r => r.AvgMaleBirdWeight > 0)
                              .Select(r => r.AvgMaleBirdWeight)
                              .DefaultIfEmpty(0)
                              .Average() * 1000,
                    cvM = reports.Where(r => r.AvgVariationCoefficientMale > 0)
                          .Select(r => r.AvgVariationCoefficientMale)
                          .DefaultIfEmpty(0)
                          .Average(),
                    unifM = reports.Where(r => r.AvgUniformityMale > 0)
                            .Select(r => r.AvgUniformityMale)
                            .DefaultIfEmpty(0)
                            .Average()
                };

                // Get all warehouse IDs from the reports
                var processedWarehouseIds = reports
                    .Where(r => r.HenBatch?.Line?.WarehouseId != null)
                    .Select(r => r.HenBatch.Line.WarehouseId)
                    .Distinct()
                    .ToList();

                return Ok(new
                {
                    batchAverages,
                    warehouseReports,
                    processedWarehouseIds
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = ex.Message });
            }
        }
        private (string, List<Guid>) ValidateHenBatches(List<HenBatch> henBatches, DateTime date)
        {
            string errorMessage = "";
            List<Guid> henBatchIds = new List<Guid>();
            IQueryable<HenBatchPerformance> performances = henBatchPerformanceService.GetAll();

            foreach (HenBatch hb in henBatches)
            {
                List<string> errors = new List<string>();
                // validate hen amount
                if (hb.HenAmountFemale == 0 && hb.HenAmountMale == 0)
                    errors.Add(this.localizer[Lang.DoesntHaveHenAmount]);

                // see if there are open hen batch performance, this means that there are reports these week
                int week = henBatchService.GetCurrentWeekNumberForDate(hb.Id, date);
                if (!performances.Any(p => p.HenBatchId == hb.Id && p.WeekNumber == week))
                    errors.Add(this.localizer[Lang.DoesntHaveHenReports]);

                if (errors.Any())
                {
                    string message = string.Format(localizer[Lang.ErrorMessage], hb.Code + "|" + hb.Line.Warehouse.Name + "|" + hb.Line.Name) + "<ul>";
                    foreach (string e in errors)
                        message = message + "<li>" + e + "</li>";

                    errorMessage += message + "</ul> </br>";
                }
                else
                    henBatchIds.Add(hb.Id);
            }

            return (errorMessage, henBatchIds);
        }

        /// <summary>
        /// returns HenBatches
        /// </summary>
        public List<SelectListItem> GetHenBatches(HenStage? henStage = null)
        {
            List<HenBatch> henBatches = henBatchService.GetAllFull(henStage).Where(hb => hb.DateEnd == null && hb.ParentId == null).ToList();

            return henBatches.Select(c => new SelectListItem(c.Code, c.Id.ToString()))
                    .ToList().OrderBy(h => h.Text).ToList();
        }

        [HttpGet]
        public List<SampleCageReportHenBatchDTO> GetActiveHenBatchesByFarm(Guid farmId, HenStage henStage)
        {
            return sampleCageReportBusinessLogic.GetActiveHenBatchesByFarm(farmId, henStage);
        }

        private List<SelectListItem> GetWarehouses(HenStage? henStage = null, string selected = null)
        {
            List<HenWarehouse> warehouses = henWarehouseService.GetAll().Where(hw => hw.Active && hw.HenStage == henStage).ToList();

            return warehouses.Select(hw => new SelectListItem(hw.DetailedName, hw.Id.ToString(), hw.Id.ToString() == selected))
                       .OrderBy(h => h.Text).ToList();
        }

        private List<SampleCageMeasurementViewModel> SetRelativeValues(List<SampleCageMeasurementViewModel> cages)
        {
            foreach (SampleCageMeasurementViewModel sample in cages)
            {
                sample.AvgFemaleBirdWeight = !string.IsNullOrEmpty(sample.FemaleBirdWeightMeasureId)
                    ? capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(sample.FemaleBirdWeightMeasureId), sample.AvgFemaleBirdWeight)
                    : sample.AvgFemaleBirdWeight;
                sample.AvgMaleBirdWeight = !string.IsNullOrEmpty(sample.MaleBirdWeightMeasureId)
                    ? capacityUnitBusinessLogic.GetBaseRelativeValue(new Guid(sample.MaleBirdWeightMeasureId), sample.AvgMaleBirdWeight)
                    : sample.AvgMaleBirdWeight;
            }
            return cages;
        }

        #endregion

        #region Validations

        public bool ValidateSampleCageRoles(HenStage? henStage = null)
        {
            if (henStage.HasValue)
            {
                switch (henStage)
                {
                    case HenStage.Laying:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingUser,
                            Roles.BackofficeLayingDailyReportsAdministrator,
                            Roles.BackofficeLayingDailyReportsUser,
                            Roles.BackofficeLayingSampleCageReportAdministrator,
                            Roles.BackofficeLayingSampleCageReportUser))
                            return false;
                        break;
                    case HenStage.Breeding:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingUser,
                            Roles.BackofficeBreedingDailyReportsAdministrator,
                            Roles.BackofficeBreedingDailyReportsUser,
                            Roles.BackofficeBreedingSampleCageReportAdministrator,
                            Roles.BackofficeBreedingSampleCageReportUser))
                            return false;
                        break;
                    default:
                        if (!this.operationContext.UserIsInRole(Roles.BackofficeSuperAdministrator))
                            return false;
                        break;
                }
                return true;
            }
            else
            {
                if (!this.operationContext.UserIsInAnyRole(
                    Roles.BackofficeSuperAdministrator,
                    Roles.BackofficeLayingAdministrator,
                    Roles.BackofficeLayingUser,
                    Roles.BackofficeLayingDailyReportsAdministrator,
                    Roles.BackofficeLayingDailyReportsUser,
                    Roles.BackofficeLayingSampleCageReportAdministrator,
                    Roles.BackofficeLayingSampleCageReportUser,
                    Roles.BackofficeBreedingAdministrator,
                    Roles.BackofficeBreedingUser,
                    Roles.BackofficeBreedingDailyReportsAdministrator,
                    Roles.BackofficeBreedingDailyReportsUser,
                    Roles.BackofficeBreedingSampleCageReportAdministrator,
                    Roles.BackofficeBreedingSampleCageReportUser))
                    return false;
                else
                    return true;
            }
        }

        public bool ValidateSampleCageRolesToDelete(HenStage? henStage = null)
        {
            if (henStage.HasValue)
            {
                switch (henStage)
                {
                    case HenStage.Laying:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingDailyReportsAdministrator,
                            Roles.BackofficeLayingSampleCageReportAdministrator))
                            return false;
                        break;
                    case HenStage.Breeding:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingDailyReportsAdministrator,
                            Roles.BackofficeBreedingSampleCageReportAdministrator))
                            return false;
                        break;
                    default:
                        if (!this.operationContext.UserIsInAnyRole(
                            Roles.BackofficeSuperAdministrator,
                            Roles.BackofficeLayingAdministrator,
                            Roles.BackofficeLayingDailyReportsAdministrator,
                            Roles.BackofficeLayingSampleCageReportAdministrator,
                            Roles.BackofficeBreedingAdministrator,
                            Roles.BackofficeBreedingDailyReportsAdministrator,
                            Roles.BackofficeBreedingSampleCageReportAdministrator))
                            return false;
                        break;
                }
                return true;
            }
            if (!this.operationContext.UserIsInAnyRole(
                        Roles.BackofficeSuperAdministrator,
                        Roles.BackofficeLayingAdministrator,
                        Roles.BackofficeLayingDailyReportsAdministrator,
                        Roles.BackofficeLayingSampleCageReportAdministrator,
                        Roles.BackofficeBreedingAdministrator,
                        Roles.BackofficeBreedingDailyReportsAdministrator,
                        Roles.BackofficeBreedingSampleCageReportAdministrator))
                return false;
            return true;
        }

        #endregion

        private List<SelectListItem> ListWithGrams => new List<SelectListItem>() { new SelectListItem(
                    capacityUnitService.Get(CapacityUnits.Grams).Symbol,
                    CapacityUnits.Grams.ToString(),
                    true) };

    }
}